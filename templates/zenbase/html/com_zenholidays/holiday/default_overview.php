<?php
defined('_JEXEC') or die;

// Create helper instance
$helper = new ZenbaseCustomHelpers();

// Import required classes
jimport('joomla.plugin.helper');
jimport('mrzen.helpers.ZenPriceHelper');
jimport('joomla.application.component.model');
jimport('joomla.application.component.helper');
use Jo<PERSON><PERSON>\Registry\Registry as JRegistry;
use Joomla\CMS\Factory as JFactory;
use <PERSON><PERSON><PERSON>\CMS\Router\Route as JRoute;
use Jo<PERSON><PERSON>\CMS\Language\Text as JText;

?>

<button class="d-md-block d-lg-none js-mobile-tabs collapsed" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab01); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab01); ?>-tab-content">
  <span><?php echo $tab01; ?></span>
</button>
<div class="<?php echo $contentClass.' '.$setActive; ?>" id="<?= $helper->toggleData($tab01); ?>-tab-content" role="tabpanel" aria-labelledby="<?= $helper->toggleData($tab01); ?>-tab">
	<!------------------------------------------------------------------------------
  // Tab/Accordion Content - Section 01
  //----------------------------------------------------------------------------->

  <?php /*
  <pre style="background: #f5f5f5; padding: 10px; margin: 10px; font-size: 12px; overflow: auto;">
Available Sections:
- Trip Highlights: <?php echo ($highlights && isset($highlights['highlights']) && isset($highlights['highlights']->items)) ? 'Yes' : 'No'; ?>

- Why Choose Us: <?php
  $whyChooseUs = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'why-choose-us');
  echo ($whyChooseUs && isset($whyChooseUs['why-choose-us']) && isset($whyChooseUs['why-choose-us']->items)) ? 'Yes' : 'No';
?>

- The EverTrek Difference: <?php
  $difference = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'difference');
  echo ($difference && isset($difference['difference']) && isset($difference['difference']->items)) ? 'Yes' : 'No';
?>

- What's Included: <?php echo ($included && isset($included['included']) && isset($included['included']->items)) ? 'Yes' : 'No'; ?>

- Trip Extensions: <?php echo (isset($this->item->activities) && !empty($this->item->activities)) ? 'Yes' : 'No'; ?>

- Trek Challenge: <?php echo (isset($actLevel['activity-level']) && isset($howChallenging) && isset($howChallenging['how-challenging']) && isset($howChallenging['how-challenging']->items)) ? 'Yes' : 'No'; ?>

- Essential Kit: <?php echo ($essentialKit && isset($essentialKit['essential-kit']) && isset($essentialKit['essential-kit']->items)) ? 'Yes' : 'No'; ?>

- Sustainable Tourism: Yes (always available)

- Customer Reviews: Yes (always available)

Available Tabs:
- Overview: Yes (always available)

- Itinerary: <?php echo isset($tab02) ? 'Yes' : 'No'; ?>

- Dates & Prices: <?php echo isset($tab03) ? 'Yes' : 'No'; ?>

- Trip Extensions: <?php echo isset($tab04) ? 'Yes' : 'No'; ?>

Note: Trip Guide, Reviews, Gallery, and FAQ tabs are not currently in use
  </pre>
  */ ?>

  <!-- Section 1: Trip Highlights -->
  <?php if ($highlights && isset($highlights['highlights']) && isset($highlights['highlights']->items)): ?>
  <div class="overview-section" id="overview-trip-highlights">
    <div class="container">
      <div class="row">
        <div class="col-12 col-lg-6">
          <h3 class="mb-5">Trip Highlights</h3>
          <?php foreach ($highlights['highlights']->items as $item): ?>
            <?php
              // Load the content into a DOM parser
              $dom = new DOMDocument();
              libxml_use_internal_errors(true);
              $dom->loadHTML(mb_convert_encoding($item->content, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
              libxml_clear_errors();

              // Find all ul elements
              $ulElements = $dom->getElementsByTagName('ul');
              foreach ($ulElements as $ul) {
                echo '<ul class="trip-highlights-list">';
                foreach ($ul->childNodes as $li) {
                  if ($li->nodeName === 'li') {
                    $content = $li->textContent;
                    // Check for {{placeholder}} anywhere in content
                    if (preg_match('/{{([^}]+)}}(.*)$/', $content, $matches)) {
                      $iconName = $matches[1];
                      $text = trim($matches[2]);
                      $iconPath = JPATH_THEMES . '/zenbase/icons/trips/' . $iconName . '.svg';
                      echo '<li class="trip-highlight-item">';
                      if (file_exists($iconPath)) {
                        $svg = file_get_contents($iconPath);
                        echo '<span class="trip-highlight-icon">' . $svg . '</span>';
                      }
                      echo '<p class="trip-highlight-text intro">' . $text . '</p>';
                      echo '</li>';
                    } else {
                      echo '<li>' . $content . '</li>';
                    }
                  }
                }
                echo '</ul>';
              }
            ?>
          <?php endforeach; ?>
        </div>

        <div class="col-12 col-lg-6">
          <div class="trip-map">
            <?php
            $tripMap = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'map');
            if ($tripMap && isset($tripMap['map']) && isset($tripMap['map']->items)) {
              foreach ($tripMap['map']->items as $map) {
                echo $map->content;
              }
            }
            ?>
          </div>
        </div>
      </div>
    </div>
  </div>
  <?php endif; ?>

  <!-- Section 2: Why choose us for this adventure? -->
  <?php
  // Get the why-choose-us copy items
  $whyChooseUs = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'why-choose-us');
  if ($whyChooseUs && isset($whyChooseUs['why-choose-us']) && isset($whyChooseUs['why-choose-us']->items)):
  ?>
  <div class="overview-section overview-section--dark" id="overview-why-choose-us">
    <div class="container">
      <div class="row g-0">
        <div class="col-12 col-md-8 col-lg-10">
          <h3 class="mb-4 text-white text-center text-md-start">Why choose us for this adventure?</h3>
          <div class="row">
            <?php
            $items = $whyChooseUs['why-choose-us']->items;
            foreach ($items as $item) {
              // Load the content into a DOM parser
              $dom = new DOMDocument();
              libxml_use_internal_errors(true);
              $dom->loadHTML(mb_convert_encoding($item->content, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
              libxml_clear_errors();

              // Find all li elements
              $liElements = $dom->getElementsByTagName('li');
              $listItems = [];
              foreach ($liElements as $li) {
                // Extract the icon placeholder and text
                $content = $li->textContent;
                $iconPlaceholder = '';
                if (preg_match('/{{(.*?)}}/', $content, $matches)) {
                  $iconPlaceholder = $matches[1];
                  $content = preg_replace('/{{.*?}}/', '', $content);
                }

                // Split content into title and description
                $parts = explode("\n", trim($content));
                $title = trim($parts[0]);
                $description = isset($parts[1]) ? trim($parts[1]) : '';

                $listItems[] = [
                  'icon' => $iconPlaceholder,
                  'title' => $title,
                  'description' => $description
                ];
              }

              // Split items into two columns
              $firstColumnItems = array_slice($listItems, 0, 3);
              $secondColumnItems = array_slice($listItems, 3);
            ?>
            <div class="col-12 col-md-6">
              <ul class="trip-highlights-list text-white">
                <?php foreach ($firstColumnItems as $listItem): ?>
                  <li class="trip-highlight-item">
                    <span class="trip-highlight-icon">
                      <?php
                      if ($listItem['icon']) {
                        include(JPATH_THEMES . '/zenbase/icons/trips/' . $listItem['icon'] . '.svg');
                      }
                      ?>
                    </span>
                    <span class="trip-highlight-text text-white">
                      <p><strong><?php echo $listItem['title']; ?></strong></p>
                      <?php if ($listItem['description']): ?>
                        <p class="sub-copy"><?php echo $listItem['description']; ?></p>
                      <?php endif; ?>
                    </span>
                  </li>
                <?php endforeach; ?>
              </ul>
            </div>
            <div class="col-12 col-md-6">
              <ul class="trip-highlights-list text-white">
                <?php foreach ($secondColumnItems as $listItem): ?>
                  <li class="trip-highlight-item">
                    <span class="trip-highlight-icon">
                      <?php
                      if ($listItem['icon']) {
                        include(JPATH_THEMES . '/zenbase/icons/trips/' . $listItem['icon'] . '.svg');
                      }
                      ?>
                    </span>
                    <span class="trip-highlight-text text-white">
                      <p><strong><?php echo $listItem['title']; ?></strong></p>
                      <?php if ($listItem['description']): ?>
                        <p class="sub-copy"><?php echo $listItem['description']; ?></p>
                      <?php endif; ?>
                    </span>
                  </li>
                <?php endforeach; ?>
              </ul>
            </div>
            <?php
            }
            ?>
          </div>
        </div>
        <div class="col-12 col-md-4 col-lg-2">
          <?php if ($holUsp && isset($holUsp['usp']) && isset($holUsp['usp']->items)): ?>
            <?php if($holGuide) : ?>
            <div class="trip-guide-panel d-none d-md-block">
              <div class="zen-media d-block d-md-flex align-items-center">
                <div class="flex-shrink-0">
                  <div class="zen-media__image">
                    <img class="img-fluid" src="https://i.assetzen.net/i/F7T9GHkRZZAy/w:73/q:90.webp" alt="Download the full Trip Guide" style="width: 66px; height: 92px;">
                  </div>
                </div>
                <div class="flex-grow-1">
                  <div class="zen-media__title">
                    <h4 class="mb-0">Download the full Trip Guide</h4>
                  </div>
                </div>
              </div>
              <?php foreach ($holGuide as $guide): ?>
                <?php foreach ($guide->items as $item): ?>
                  <a href="/brochure-download?automation=<?= $item->xreference; ?>&subject=<?= $name; ?>" class="d-none d-md-block btn btn-primary btn-outline mx-auto mt-3" target="_blank" rel="noopener noreferrer">
                    Download guide
                    <img src="/templates/zenbase/icons/trips/download.svg" alt="Download icon" class="ms-2">
                  </a>
                <?php endforeach; ?>
              <?php endforeach; ?>
            </div>
            <?php endif; ?>
            <div class="mt-3">
            <?php if($holGuide) : ?>
                <?php foreach ($holGuide as $guide): ?>
                  <?php foreach ($guide->items as $item): ?>
                    <a href="/brochure-download?automation=<?= $item->xreference; ?>&subject=<?= $name; ?>" class="d-block d-md-none btn btn-primary bg-white bd-white mx-auto my-3" target="_blank" rel="noopener noreferrer">
                      Download guide
                      <img src="/templates/zenbase/icons/trips/download.svg" alt="Download icon" class="ms-2">
                    </a>
                  <?php endforeach; ?>
                <?php endforeach; ?>
              <?php endif; ?>
              <a href="#dates-prices" id="dates-prices-button" class="btn btn-primary w-100 d-flex align-items-center justify-content-center gap-2">
                <span class="d-flex align-items-center">Dates & Prices</span>
                <img src="/templates/zenbase/icons/trips/date_range.svg" alt="Date range icon" class="d-flex align-items-center">
              </a>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
  <?php endif; ?>

  <!-- Section 3: The EverTrek Difference -->
  <?php
  $difference = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'difference');
  if ($difference && isset($difference['difference']) && isset($difference['difference']->items)):
    $evertrekDifference = json_decode(strip_tags($difference['difference']->items[0]->content));
    if ($evertrekDifference && isset($evertrekDifference->template)) {
      include JPATH_BASE . '/templates/zenbase/html/partials/evertrek_difference_' . $evertrekDifference->template . '.php';
    } else {
      include JPATH_BASE . '/templates/zenbase/html/partials/evertrek_difference_1.php';
    }
  endif;
  ?>

  <style>
    #overview-the-evertrek-difference {
      background-color: #e3e8ee;
      padding: 35px 0;
    }
    .evertrek-difference-intro {
      text-align: left;
      background-color: #247ba0;
      border-radius: 10px;
      padding: 25px;
      color: white;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .evertrek-difference-intro h2 {
      margin-bottom: 16px;
      color: white;
    }
    .evertrek-difference-intro p {
      color: white;
      margin: 0;
    }
    .evertrek-difference-items {
      display: flex;
      flex-direction: column;
      gap: 40px;
      flex: 0 1 auto;
    }
    .evertrek-difference-item {
      padding: 0;
      background: none;
      flex: 0 1 auto;
      display: flex;
      flex-direction: column;
    }
    .evertrek-difference-item h3 {
      margin: 24px 0 8px;
    }
    .evertrek-difference-item h3,
    .evertrek-difference-grid h3 {
      color: #247ba0;
    }
    .evertrek-difference-image-wrapper {
      background-color: #bdd2de;
      border-radius: 10px;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 0 0 auto;
    }
    .evertrek-difference-image {
      height: 140px;
      width: auto;
      border-radius: 10px;
    }
    .training-plan-banner {
      background: white;
      border: 1px solid #909090;
      border-radius: 10px;
      color: black;
      text-align: center;
      width: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    .training-plan-banner h3,
    .training-plan-banner p {
      color: black;
      margin: 0;
    }
    .training-plan-icon {
      background-color: #fe7720;
      width: 100%;
      height: 80px; /* was 93px */
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 0;
    }
    .training-plan-content {
      padding: 0 11px 19px 11px;
    }

    .training-plan-content h3 {
      margin-bottom: 12px;
      margin-top: 16px;
    }

    @media (min-width: 992px) {
      .evertrek-difference-items {
        flex-direction: row;
        gap: 10px;
        flex: 0 1 auto;
      }
      .evertrek-difference-item {
        flex: 1 1 0;
      }
      .training-plan-banner {
        display: flex;
        align-items: center;
        text-align: left;
        flex-direction: row;
      }
      .training-plan-icon {
        margin: 0 24px 0 0;
        width: 93px;
      }
      #the-evertrek-difference .row {
        display: flex;
        align-items: stretch;
      }
      #the-evertrek-difference .row:first-child {
        margin-bottom: 20px;
      }
      #the-evertrek-difference .row > div {
        display: flex;
        flex: 0 1 auto;
      }
    }
    #why-choose-us {
      padding: 40px 0;
    }
  </style>

  <!-- Section 4: What's Included -->
  <?php
  // Force reload the included items to ensure we have the latest data
  $included = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'included');
  $excluded = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'excluded');
  ?>





  <?php
  // Get included items with proper state check
  $db = JFactory::getDbo();
  $query = $db->getQuery(true);
  $query->select('ci.id, ci.title, ci.content')
        ->from('#__zencopyitems AS ci')
        ->join('LEFT', '#__zencopyusages AS cu ON ci.id = cu.item_id')
        ->join('LEFT', '#__categories AS cat ON ci.catid = cat.id')
        ->where('cu.extension = ' . $db->quote('com_zenholidays'))
        ->where('cu.extension_id = ' . (int)$id)
        ->where('cat.alias = ' . $db->quote('included'))
        ->where('ci.state = 1')
        ->where('cu.state = 1');
  $db->setQuery($query);
  $includedItems = $db->loadObjectList();

  // Check if we have valid included items
  if (!empty($includedItems)):
  ?>
  <div class="overview-section" id="overview-whats-included">
    <div class="container">
      <!-- Preview version (default visible) -->
      <div id="whats-included-preview">
        <h3 class="my-3">What's Included</h3>
        <div class="row">
          <div class="col-md-6">
            <div class="zen-rte zen-rte--default-list-ticks">
              <?php
              if (!empty($includedItems) && isset($includedItems[0]->content)) {
                try {
                  $dom = new DOMDocument();
                  libxml_use_internal_errors(true);
                  $dom->loadHTML(mb_convert_encoding($includedItems[0]->content, 'HTML-ENTITIES', 'UTF-8'));
                  libxml_clear_errors();

                  $bullets = $dom->getElementsByTagName('li');
                  $totalBullets = $bullets->length;

                  // Calculate how many items to show in each column
                  $itemsPerColumn = ceil($totalBullets / 2);

                  echo '<ul>';
                  $bulletCount = 0;
                  foreach ($bullets as $bullet) {
                    if ($bulletCount < $itemsPerColumn) {
                      echo '<li>' . $bullet->textContent . '</li>';
                    }
                    $bulletCount++;
                  }
                  echo '</ul>';
                } catch (Exception $e) {
                  // Fallback if DOM parsing fails
                  echo $includedItems[0]->content;
                }
              }
              ?>
            </div>
          </div>
          <div class="col-md-6">
            <div class="zen-rte zen-rte--default-list-ticks">
              <?php
              if (!empty($includedItems) && isset($includedItems[0]->content)) {
                try {
                  $dom = new DOMDocument();
                  libxml_use_internal_errors(true);
                  $dom->loadHTML(mb_convert_encoding($includedItems[0]->content, 'HTML-ENTITIES', 'UTF-8'));
                  libxml_clear_errors();

                  $bullets = $dom->getElementsByTagName('li');
                  $totalBullets = $bullets->length;

                  // Calculate how many items to show in each column
                  $itemsPerColumn = ceil($totalBullets / 2);

                  echo '<ul>';
                  $bulletCount = 0;
                  foreach ($bullets as $bullet) {
                    if ($bulletCount >= $itemsPerColumn) {
                      echo '<li>' . $bullet->textContent . '</li>';
                    }
                    $bulletCount++;
                  }
                  echo '</ul>';
                } catch (Exception $e) {
                  // No need to output anything here as the content is already shown in the first column as fallback
                }
              }
              ?>
            </div>
          </div>
        </div>
      </div>

      <!-- Full version (default hidden) -->
      <div id="whats-included-full" style="display: none;">
        <div class="row">
          <div class="col-md-6">
            <h3 class="my-3">What's Included</h3>
            <div class="zen-rte zen-rte--default-list-ticks">
              <?php
              if (!empty($includedItems) && isset($includedItems[0]->content)) {
                echo $includedItems[0]->content;
              }
              ?>
            </div>
          </div>

          <div class="col-md-6">
            <h3 class="my-3">What's Not Included</h3>
            <div class="zen-rte zen-rte--default-list-crosses">
             <?php
             // Get excluded items with proper state check
             $db = JFactory::getDbo();
             $query = $db->getQuery(true);
             $query->select('ci.id, ci.title, ci.content')
                   ->from('#__zencopyitems AS ci')
                   ->join('LEFT', '#__zencopyusages AS cu ON ci.id = cu.item_id')
                   ->join('LEFT', '#__categories AS cat ON ci.catid = cat.id')
                   ->where('cu.extension = ' . $db->quote('com_zenholidays'))
                   ->where('cu.extension_id = ' . (int)$id)
                   ->where('cat.alias = ' . $db->quote('excluded'))
                   ->where('ci.state = 1')
                   ->where('cu.state = 1');
             $db->setQuery($query);
             $excludedItems = $db->loadObjectList();

             if (!empty($excludedItems)):
               foreach ($excludedItems as $item):
                 echo($item->content);
               endforeach;
             else:
             ?>
               <ul>
                 <li>International flights</li>
                 <li>Travel insurance</li>
                 <li>Visa fees</li>
                 <li>Personal expenses</li>
               </ul>
             <?php endif; ?>
            </div>
          </div>
        </div>
      </div>

      <!-- Toggle button -->
      <div class="text-left mt-4">
        <button class="trek-challenge-toggle" id="overview-whats-included-toggle" onclick="toggleWhatsIncluded()" data-state="more">
          See more
          <img src="/templates/zenbase/icons/trips/expand_more.svg" alt="Expand more" class="ms-2">
        </button>
      </div>
    </div>
  </div>
  <?php endif; ?>

  <script>
    function toggleWhatsIncluded() {
      const preview = document.getElementById('whats-included-preview');
      const full = document.getElementById('whats-included-full');
      const button = document.getElementById('overview-whats-included-toggle');

      if (preview.style.display !== 'none') {
        preview.style.display = 'none';
        full.style.display = 'block';
        button.innerHTML = 'See less <img src="/templates/zenbase/icons/trips/expand_more.svg" alt="Expand more" class="ms-2">';
        button.setAttribute('data-state', 'less');
      } else {
        preview.style.display = 'block';
        full.style.display = 'none';
        button.innerHTML = 'See more <img src="/templates/zenbase/icons/trips/expand_more.svg" alt="Expand more" class="ms-2">';
        button.setAttribute('data-state', 'more');
      }
    }
  </script>

  <style>
    #overview-whats-included-toggle {
      display: inline-flex;
      align-items: center;
      background: transparent;
      border: 1px solid white;
      color: white;
      padding: 6px 12px 6px 24px;
      border-radius: 100px;
      font-size: 16px;
      line-height: 1.5;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    #overview-whats-included-toggle:hover {
      background: rgba(255,255,255,0.1);
    }
    #overview-whats-included-toggle img {
      transition: transform 0.2s ease;
    }
    #overview-whats-included-toggle:not([data-state="more"]) img {
      transform: rotate(180deg);
    }
    /* Legacy support for old ID */
    #whats-included-toggle {
      display: inline-flex;
      align-items: center;
      background: transparent;
      border: 1px solid white;
      color: white;
      padding: 6px 12px 6px 24px;
      border-radius: 100px;
      font-size: 16px;
      line-height: 1.5;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    #whats-included-toggle:hover {
      background: rgba(255,255,255,0.1);
    }
    #whats-included-toggle img {
      transition: transform 0.2s ease;
    }
    #whats-included-toggle:not([data-state="more"]) img {
      transform: rotate(180deg);
    }
  </style>

  <!-- Section 5: Trip Extensions OR Customer Reviews (if no extensions) -->
  <?php if (isset($this->item->activities) && !empty($this->item->activities)): ?>
  <!-- Trip Extensions Section -->
  <div class="overview-section" id="overview-trip-extensions">
    <div class="container">
      <div class="d-flex justify-content-between align-items-end mb-4">
        <div>
          <h2 class="section-title">Trip Extensions</h2>
          <?php if (count($this->item->activities) > 1): ?>
          <p class="section-intro">Enhance your adventure with our range of trip extensions.</p>
          <?php endif; ?>
        </div>
        <div class="col-12 col-md-2">
          <?php if (count($this->item->activities) > 1): ?>
          <a href="#trip-extensions" class="btn btn-primary rounded-pill">
            View all Trip Extensions
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-2">
              <path d="M9.00002 7.21051C8.61002 7.60051 8.61002 8.23051 9.00002 8.62051L12.88 12.5005L9.00002 16.3805C8.61002 16.7705 8.61002 17.4005 9.00002 17.7905C9.39002 18.1805 10.02 18.1805 10.41 17.7905L15 13.2005C15.39 12.8105 15.39 12.1805 15 11.7905L10.41 7.20051C10.03 6.82051 9.39002 6.82051 9.00002 7.21051Z" fill="currentColor"/>
            </svg>
          </a>
          <?php endif; ?>
        </div>
      </div>

      <?php
      // Get the extension relationship type ID from the evertrek plugin params
      $plugin = JPluginHelper::getPlugin('mrzen', 'evertrek');
      $pluginParams = new JRegistry($plugin->params);
      $extensionTypeId = $pluginParams->get('extension_relationship_type_id');
      $fuzziness = $pluginParams->get('extension_fuzziness', 0);

      // Get extensions (needed for template, but no debug output)
      $db = JFactory::getDbo();
      $query = $db->getQuery(true)
          ->select('DISTINCT h.id, h.name, h.alias, p.id as price_id, d.start_date, d.end_date')
          ->from($db->quoteName('#__zenholidaysrelations', 'r'))
          ->join('INNER', $db->quoteName('#__zenholidays', 'h') . ' ON h.id = r.right_holiday_id')
          ->join('INNER', $db->quoteName('#__zenholidaydates', 'd') . ' ON d.holiday_id = h.id')
          ->join('INNER', $db->quoteName('#__zenholidayprices', 'p') . ' ON p.date_id = d.id')
          ->where('r.type_id = ' . $db->quote($extensionTypeId))
          ->where('r.left_holiday_id = ' . $db->quote($this->item->id))
          ->where('r.state = 1')
          ->where('h.state = 1')
          ->where('d.state = 1')
          ->where('p.state = 1');

      if ($fuzziness > 0) {
          $query->where('(
              d.start_date BETWEEN DATE_ADD(' . $db->quote($this->item->end_date) . ', INTERVAL 0 DAY)
              AND DATE_ADD(' . $db->quote($this->item->end_date) . ', INTERVAL ' . $fuzziness . ' DAY)
              OR
              d.end_date BETWEEN DATE_SUB(' . $db->quote($this->item->start_date) . ', INTERVAL ' . $fuzziness . ' DAY)
              AND ' . $db->quote($this->item->start_date) . '
          )');
      }

      $db->setQuery($query);
      $extensions = $db->loadObjectList();

      // Load activities model
      require_once JPATH_ROOT . '/components/com_zenactivities/models/activities.php';
      $activitiesModel = new ZenActivitiesModelActivities();

      // Get activities for this holiday using the same method as main branch
      $activityUpgrades = isset($this->item->activities) ? $this->item->activities : [];

      ?>

      <div class="row">
        <?php foreach ($activityUpgrades as $upgrade): ?>
          <div class="col-12 mb-4">
            <div class="extension-card">
              <div class="extension-card__inner">
                <div class="extension-card__image">
                  <?php if (isset($upgrade->images[0])): ?>
                    <?php $firstImage = JHtmlImages::getCachedImageUrl($upgrade->images[0], 600, 300, '', array(), $upgrade->images[0]->title); ?>
                    <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($upgrade->images[0]->title); ?>">
                  <?php else: ?>
                    <img src="https://via.placeholder.com/600x300" alt="<?php echo htmlspecialchars($upgrade->title ?: $upgrade->name); ?>">
                  <?php endif; ?>
                </div>
                <div class="extension-card__content">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <h3><?php echo htmlspecialchars($upgrade->title); ?></h3>
                    <div class="extension-card__price">
                      <?php if (isset($upgrade->price)): ?>
                        <?php if ($upgrade->price->currency_code === 'GBP'): ?>
                          £<?php echo number_format($upgrade->price->value, 0); ?>
                        <?php else: ?>
                          <?php echo $upgrade->price->currency_code . ' ' . number_format($upgrade->price->value, 0); ?>
                        <?php endif; ?>
                      <?php endif; ?>
                    </div>
                  </div>
                  <div class="extension-card__description mb-3">
                    <?php
                    $description = $upgrade->description;
                    // Remove img elements from the description
                    $description = preg_replace('/<img[^>]*>/', '', $description);
                    $firstParagraph = preg_match('/<p>(.*?)<\/p>/', $description, $matches) ? $matches[0] : $description;
                    echo $firstParagraph;
                    ?>
                  </div>
                  <?php
                  // Use the trip-extensions tab as the target for the "More details" link
                  // Format for deep-link-to-tabs.js is just the tab ID without the -tab-content suffix
                  $moreDetailsUrl = '#trip-extensions';
                  ?>
                  <a href="<?php echo $moreDetailsUrl; ?>" class="extension-card__link">More details</a>
                </div>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
      </div>

    </div>
  </div>
  <?php else: ?>
  <!-- Customer Reviews Section (when no trip extensions available) -->
  <?php if (isset($ugcReviews) && !empty($ugcReviews)): ?>
  <div class="overview-section" id="overview-customer-reviews-alt">
    <div class="container">
      <h3 class="mb-4">What customers say about us</h3>
      <!-- Section content -->
       <div>
<!-- <div class="embedsocial-hashtag" data-ref="816b4a7734f4188b5848cf0b506066f9b374b3ed"></div> <script> (function(d, s, id) { var js; if (d.getElementById(id)) {return;} js = d.createElement(s); js.id = id; js.src = "https://embedsocial.com/cdn/ht.js"; d.getElementsByTagName("head")[0].appendChild(js); }(document, "script", "EmbedSocialHashtagScript")); </script> -->

  <div class="embedsocial-widget" data-ref="fc2d1a20d46e3a578af0152614a2d130" style=""></div> <script> (function(d, s, id) { var js; if (d.getElementById(id)) {return;} js = d.createElement(s); js.id = id; js.src = "https://embedsocial.com/cdn/aht.js"; d.getElementsByTagName("head")[0].appendChild(js); }(document, "script", "EmbedSocialWidgetScript")); </script>

<style>
          .embedsocial-widget {
          width: 100%;
          max-width: 100%;
          }
          @media screen and (min-width: 767px) {
          .embedsocial-widget {
            max-width: 1440px;
          }
          .embedsocial-widget iframe {
            margin: -2.5rem -1.25rem;
            width: 103%!important;
          }

</style>

          <style>
          .embedsocial-widget {
          width: 100%;
          max-width: 100%;
          }
          @media screen and (min-width: 767px) {
          .embedsocial-widget {
            max-width: 1440px;
          }
          .embedsocial-widget iframe {
            margin: -2.5rem -1.25rem;
            width: 103%!important;
          }
          }

          </style>
       </div>
    </div>
	</div>
  <?php endif; ?>
  <?php endif; ?>

  <style>
    #trip-extensions {
      background-color: #f2f2f2;
      padding: 80px 0;
    }
    .section-intro {
      font-size: 1.25rem;
      margin: 0;
    }
    .extension-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      height: 100%;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    #overview-trip-extensions .extension-card__inner {
      display: flex;
      height: 100%;
    }
    .extension-card__image {
      flex: 0 0 25%;
      position: relative;
      overflow: hidden;
    }
    .extension-card__image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      top: 0;
      left: 0;
    }
    .extension-card__content {
      flex: 1;
      padding: 24px 32px;
    }
    /* .extension-card__content h3 {
      margin: 0;
      font-size: 18px;
      line-height: 22px;
      font-weight: 700;
    } */
    .extension-card__description {
      color: #666;
      margin: 0;
      font-size: 1.1rem;
      line-height: 1.5;
    }
    .extension-card__price {
      font-size: 18px;
      line-height: 22px;
      font-weight: 600;
      color: #FF7F41;
    }

    /*
     * Note: The "More details" links in the trip extensions section use the deep-link-button-handlers.js script
     * to handle clicks and navigate to the Trip Extensions tab. The updated script ensures these links work
     * consistently even when clicked multiple times.
     */
    .extension-card__link {
      color: black;
      text-decoration: underline;
      font-weight: 500;
      font-size: 14px;
    }
    .extension-card__link:hover {
      text-decoration: none;
    }
    .btn-primary {
      background: #FF7F41;
      border: none;
      padding: 12px 24px;
      color: white;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
    }
    .btn-primary:hover {
      background: #e67339;
    }
    .btn-primary img {
      width: 20px;
      height: 20px;
    }
    /* Ensure "View all Trip Extensions" button text stays on one line */
    .btn.btn-primary.rounded-pill {
      white-space: nowrap;
    }

    @media (max-width: 767px) {
      .extension-card__inner {
        flex-direction: column;
      }
      .extension-card__image {
        flex: 0 0 200px;
        position: relative;
      }
      .extension-card__image img {
        position: relative;
      }
      .extension-card__content {
        padding: 20px;
      }
      /* .extension-card__content h3 {
        font-size: 1.5rem;
      } */
      .extension-card__price {
        font-size: 1.5rem;
        display: none;
      }
      #trip-extensions .extension-card__description {
        display: none;
      }
      .sustainable-tourism-panel h3,
      #overview-essential-kit {
        padding: 40px 0;
      }
      #overview-essential-kit h3,
      #why-choose-us h3 {
        font-size: 24px;
        line-height: 26px;
        font-weight: 800;
      }
      .sustainable-tourism-panel div div {
       text-align: center;
      }
      .evertrek-difference-items {
        gap: 10px;
        margin-bottom: 20px;
        margin-top: 10px;
      }
    }
  </style>

  <script>
    // Helper function for logging
    function log(...args) {
      if (window.DEBUG) {
        console.log('[TabNavigation]', ...args);
      }
    }

    // Define isValidTabKey function if it doesn't exist
    if (!window.isValidTabKey) {
      window.isValidTabKey = function(key) {
        if (!key) return false;

        // Check desktop tabs
        const desktopTab = document.getElementById(key + '-tab');
        if (desktopTab) return true;

        // Check mobile accordions
        const mobileAccordion = document.querySelector(`[data-bs-target="#${key}-tab-content"]`);
        if (mobileAccordion) return true;

        return false;
      };
    }

    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM Content Loaded - Setting up tab navigation');
      const heroButton = document.getElementById('hero-dates-prices-button');
      const overviewButton = document.getElementById('dates-prices-button');

      // Function to handle tab activation with proper scrolling
      function activateTabWithScroll(tabId) {
        console.log('Activating tab:', tabId);

        // Get the tab elements
        const desktopTab = document.getElementById(tabId + '-tab');
        const mobileAccordion = document.querySelector(`[data-bs-target="#${tabId}-tab-content"]`);
        const tabContent = document.getElementById(tabId + '-tab-content');
        const isMobile = window.innerWidth < 992;

        if (isMobile && mobileAccordion && tabContent) {
          // Mobile: Open accordion
          try {
            console.log('Activating mobile accordion');

            // First, close any open accordions
            const openAccordions = document.querySelectorAll('.collapse.show');
            openAccordions.forEach(accordion => {
              if (accordion.id !== tabContent.id) {
                try {
                  const collapse = bootstrap.Collapse.getInstance(accordion);
                  if (collapse) collapse.hide();
                } catch (e) {
                  console.error('Error closing accordion:', e);
                }
              }
            });

            // Now open our target accordion
            const collapse = new bootstrap.Collapse(tabContent, { toggle: true });

            // Scroll to the accordion after it's shown
            tabContent.addEventListener('shown.bs.collapse', function scrollToAccordion() {
              console.log('Scrolling to mobile accordion');
              const rect = mobileAccordion.getBoundingClientRect();
              const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
              const offsetPosition = rect.top + scrollTop - 100;

              window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
              });

              tabContent.removeEventListener('shown.bs.collapse', scrollToAccordion);
            });
          } catch (error) {
            console.error('Error opening accordion:', error);
          }
        } else if (desktopTab) {
          // Desktop: Activate tab
          try {
            console.log('Activating desktop tab');

            // Make sure all tabs are properly initialized
            const allTabs = document.querySelectorAll('.zen-tab__container.nav a[data-bs-toggle="tab"]');
            allTabs.forEach(tab => {
              if (!bootstrap.Tab.getInstance(tab)) {
                try {
                  new bootstrap.Tab(tab);
                } catch (e) {
                  console.error('Error initializing tab:', e);
                }
              }
            });

            const tab = new bootstrap.Tab(desktopTab);
            tab.show();

            // Scroll to the tab after it's shown
            desktopTab.addEventListener('shown.bs.tab', function scrollToTab() {
              console.log('Scrolling to desktop tab');
              const tabsContainer = document.getElementById('desktop-tabs');
              if (tabsContainer) {
                const rect = tabsContainer.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const offsetPosition = rect.top + scrollTop - 50;

                window.scrollTo({
                  top: offsetPosition,
                  behavior: 'smooth'
                });
              }

              desktopTab.removeEventListener('shown.bs.tab', scrollToTab);
            });
          } catch (error) {
            console.error('Error activating tab:', error);
          }
        } else {
          console.warn('No valid tab or accordion found for:', tabId);
        }

        // Update URL hash
        history.replaceState({ tab: tabId }, '', '#' + tabId);
      }

      // With deep-link-to-tabs.js, we can simplify this code
      // The script will automatically handle the hash changes and activate the appropriate tabs

      // For the dates-prices buttons, we'll just update the hash
      if (overviewButton) {
        overviewButton.addEventListener('click', function(e) {
          e.preventDefault();
          console.log('Overview Dates & Prices button clicked');

          // Use the global updateUrlHash function to update the URL hash
          // This will update the URL and trigger navigation without adding to browser history
          console.log('Using updateUrlHash for dates-prices');
          if (window.updateUrlHash) {
            window.updateUrlHash('dates-prices');
          } else {
            // Fallback if updateUrlHash is not available
            location.replace('#dates-prices');
          }
        });
      }

      // For trip extension "More details" links and "View all Trip Extensions" button, we'll just update the hash
      document.querySelectorAll('.extension-card__link, .btn.btn-primary.rounded-pill[href="#trip-extensions"]').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          console.log('Trip extension link clicked');

          // Use the global updateUrlHash function to update the URL hash
          // This will update the URL and trigger navigation without adding to browser history
          console.log('Using updateUrlHash for trip-extensions');
          if (window.updateUrlHash) {
            window.updateUrlHash('trip-extensions');
          } else {
            // Fallback if updateUrlHash is not available
            location.replace('#trip-extensions');
          }
        });
      });
    });
  </script>

  <!-- Section 6: Trek Challenge -->
  <?php if (isset($actLevel['activity-level']) && isset($howChallenging) && isset($howChallenging['how-challenging']) && isset($howChallenging['how-challenging']->items)): ?>
  <div class="overview-section" id="overview-trek-challenge">
    <div class="container">
      <div class="trek-challenge-header">
        <div class="difficulty-gauge">
          <?php
          // Get the difficulty level and corresponding image
          $difficultyLevel = strtolower($actLevel['activity-level']);
          $difficultyImage = 'grade-1.svg'; // Default to Easy

          if ($difficultyLevel === 'hardcore') {
            $difficultyImage = 'grade-4.svg';
          } elseif ($difficultyLevel === 'challenging') {
            $difficultyImage = 'grade-3.svg';
          } elseif ($difficultyLevel === 'moderate') {
            $difficultyImage = 'grade-2.svg';
          }
          ?>
          <img src="/templates/zenbase/images/grading/<?php echo $difficultyImage; ?>" alt="Difficulty Gauge">
          <div class="difficulty-label">
            <p class="mb-0">Difficulty:</p>
            <h4 class="mb-0"><?php echo $actLevel['activity-level']; ?></h4>
          </div>
        </div>
        <?php
        // Get the how-challenging copy items
        $howChallenging = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'how-challenging');
        if ($howChallenging && isset($howChallenging['how-challenging']) && isset($howChallenging['how-challenging']->items)):
          $content = $howChallenging['how-challenging']->items[0]->content;
          // Remove data-start and data-end attributes before processing
          $content = preg_replace('/\s+data-start="[^"]*"/', '', $content);
          $content = preg_replace('/\s+data-end="[^"]*"/', '', $content);
          $content = preg_replace('/\s+data-is-last-node="[^"]*"/', '', $content);
          $sections = explode('<hr />', $content);
        ?>
        <div class="trek-challenge-intro">
          <h3>How challenging is the trek?</h3>
          <?php echo $sections[0]; ?>
        </div>
      </div>

      <div class="trek-challenge-content">
        <div class="white-rule"></div>
        <?php
        // Load the content into a DOM parser to add classes
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML(mb_convert_encoding($sections[1], 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();

        // Add class to ul elements
        $ulElements = $dom->getElementsByTagName('ul');
        foreach ($ulElements as $ul) {
          $ul->setAttribute('class', 'trek-challenge-list');
        }

        echo $dom->saveHTML();
        ?>

        <div class="collapse" id="overview-trek-expectations">
          <div class="trek-expectations-content">
            <?php
            // First, check if the content contains an h4 tag
            if (preg_match('/<h4>(.*?)<\/h4>/is', $sections[2], $matches)) {
              // Extract the h4 content
              $h4Content = $matches[1];

              // Output the h4 separately
              echo "<h4>{$h4Content}</h4>";

              // Process the rest of the content
              $dom = new DOMDocument();
              libxml_use_internal_errors(true);

              // Remove the h4 tag from the content before processing
              $contentWithoutH4 = preg_replace('/<h4>.*?<\/h4>/is', '', $sections[2], 1);
              $dom->loadHTML(mb_convert_encoding($contentWithoutH4, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
              libxml_clear_errors();

              // Add class to ol elements if they exist
              $olElements = $dom->getElementsByTagName('ol');
              foreach ($olElements as $ol) {
                $ol->setAttribute('class', 'trek-expectations-list');
              }

              echo $dom->saveHTML();
            } else {
              // If no h4 tag is found, process the content as before
              $dom = new DOMDocument();
              libxml_use_internal_errors(true);
              $dom->loadHTML(mb_convert_encoding($sections[2], 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
              libxml_clear_errors();

              // Add class to ol elements
              $olElements = $dom->getElementsByTagName('ol');
              foreach ($olElements as $ol) {
                $ol->setAttribute('class', 'trek-expectations-list');
              }

              echo $dom->saveHTML();
            }
            ?>
          </div>
        </div>

        <div class="trek-challenge-buttons">
          <button class="trek-challenge-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#overview-trek-expectations" aria-expanded="false" aria-controls="overview-trek-expectations" id="overview-trek-expectations-more">
            What To Expect On The Trek
            <img src="/templates/zenbase/icons/trips/expand_more.svg" alt="Expand more" class="ms-2">
          </button>
          <button class="trek-challenge-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#overview-trek-expectations" aria-expanded="true" aria-controls="overview-trek-expectations" id="overview-trek-expectations-less">
            See Less
            <img src="/templates/zenbase/icons/trips/expand_more.svg" alt="Expand more" class="ms-2">
          </button>
        </div>
      </div>
      <?php endif; ?>
    </div>
  </div>
  <?php endif; ?>
  <style>
    #overview-trek-challenge {
      background-color: #247ba0;
      padding: 40px 0;
    }
    #overview-trek-challenge h3,
    #overview-trek-challenge h4,
    #overview-trek-challenge p,
    #overview-trek-challenge li {
      color: white;
    }
    #overview-trek-challenge h4 {
      margin-bottom: 24px;
    }
    .trek-challenge-header {
      display: flex;
      gap: 40px;
      margin-bottom: 32px;
    }
    .difficulty-gauge {
      background: white;
      border-radius: 10px;
      padding: 16px;
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 0 0 auto;
    }
    .difficulty-gauge img {
      width: 92px;
      height: auto;
    }
    .difficulty-label {
      flex: 1;
      text-align: left;
    }
    .difficulty-label p {
      color: #666 !important;
      font-size: 14px;
    }
    .difficulty-label h4 {
      color: #000 !important;
      font-size: 20px;
    }
    .trek-challenge-intro {
      flex: 1;
    }
    .white-rule {
      height: 1px;
      background-color: white;
      margin: 24px 0;
    }
    .-content {
      margin-bottom: 32px;
    }
    .trek-challenge-list {
      list-style: none;
      padding: 0;
      margin: 0 0 1rem;
    }
    .trek-challenge-list li {
      position: relative;
      padding-left: 24px;
      margin-bottom: 1rem;
    }
    .trek-challenge-list li:before {
      content: "•";
      position: absolute;
      left: 0;
      color: #FF7F41;
    }
    .trek-challenge-toggle {
      display: inline-flex;
      align-items: center;
      background: transparent;
      border: 1px solid white;
      color: white;
      padding: 6px 12px 6px 20px;
      border-radius: 9999px;
      font-size: 16px;
      line-height: 1.5;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .trek-challenge-toggle:hover {
      background: rgba(255,255,255,0.1);
    }
    .trek-expectations-content {
      margin-top: 10px;
    }
    .trek-expectations-list {
      list-style: none;
      counter-reset: trek-counter;
      padding: 0;
      margin: 0;
    }
    .trek-expectations-list li {
      counter-increment: trek-counter;
      position: relative;
      padding-left: 40px;
      margin-bottom: 24px;
    }
    .trek-expectations-list li:before {
      content: counter(trek-counter);
      position: absolute;
      left: 0;
      top: 0;
      color: #FF7F41;
      font-weight: bold;
      font-size: 20px;
    }
    .trek-expectations-list li strong {
      display: block;
      margin-bottom: 4px;
    }
    #overview-trek-expectations-less {
      display: none;
    }
    #overview-trek-expectations.show ~ .trek-challenge-buttons #overview-trek-expectations-more {
      display: none;
    }
    #overview-trek-expectations.show ~ .trek-challenge-buttons #overview-trek-expectations-less {
      display: inline-flex;
    }
    #overview-trek-expectations {
      transition: all .5s ease;
      overflow:hidden;
    }
    #overview-trek-expectations.collapsing {
      transition-duration: .5s;
    }
    #overview-trek-expectations-less img {
      transform: scaleY(-1);
    }
    @media (min-width: 992px) {
      .difficulty-gauge {
        flex-direction: column;
        text-align: center;
      }
      .difficulty-label {
        text-align: center;
      }
    }
    @media (max-width: 991px) {
      .trek-challenge-header {
        flex-direction: column;
        gap: 24px;
      }
    }
  </style>

  <!-- Section 7: Essential Kit -->
  <?php if ($essentialKit && isset($essentialKit['essential-kit']) && isset($essentialKit['essential-kit']->items)): ?>
  <div class="overview-section" id="overview-essential-kit">
    <div class="container">
      <div class="row d-flex align-items-end mb-5">
        <div class="col-lg-9">
          <h3 class="mb-4">Essential Kit</h3>
          <p class="intro mb-0">Take a look at the recommended essential kit below to ensure you're fully equipped and ready to enjoy your trip to the fullest. A full and detailed packing list can be downloaded.</p>
        </div>
        <div class="col-lg-3 d-flex justify-content-lg-end">
          <?php
          $packingList = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'packing-list');
          if ($packingList && isset($packingList['packing-list']) && isset($packingList['packing-list']->items)):
            // Strip <p> tags from content before JSON decode
            $content = preg_replace('/^<p[^>]*>(.*)<\/p>$/s', '$1', $packingList['packing-list']->items[0]->content);
            $formData = json_decode($content, true);
            if ($formData && isset($formData['form_url'], $formData['iframe_id'], $formData['form_id'], $formData['form_name'], $formData['form_title'])):
          ?>
          <button type="button" class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#packingListModal">
            Download full packing list
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-2">
              <path d="M9.00002 7.21051C8.61002 7.60051 8.61002 8.23051 9.00002 8.62051L12.88 12.5005L9.00002 16.3805C8.61002 16.7705 8.61002 17.4005 9.00002 17.7905C9.39002 18.1805 10.02 18.1805 10.41 17.7905L15 13.2005C15.39 12.8105 15.39 12.1805 15 11.7905L10.41 7.20051C10.03 6.82051 9.39002 6.82051 9.00002 7.21051Z" fill="currentColor"/>
            </svg>
          </button>
          <?php
            endif;
          endif;
          ?>
        </div>
      </div>
      <div class="row gx-0">
        <?php if ($essentialKit && isset($essentialKit['essential-kit']) && isset($essentialKit['essential-kit']->items)): ?>
          <?php foreach ($essentialKit['essential-kit']->items as $item): ?>
            <?php
              // Strip wrapping <p> tags from content before JSON decode
              $content = preg_replace('/^<p[^>]*>(.*)<\/p>$/s', '$1', $item->content);
              $kitData = json_decode($content, true);
              $jsonError = json_last_error();
              $jsonErrorMsg = json_last_error_msg();
              $kitItems = isset($kitData['products']) ? $kitData['products'] : null;
              $freeEquipmentHire = isset($kitData['free_equipment_hire']) ? $kitData['free_equipment_hire'] === 'true' : false;
              $itemCount = is_array($kitItems) ? count($kitItems) : 0;
            ?>
            <div class="col-12 <?php echo $freeEquipmentHire ? 'col-md-8 col-split-65' : 'col-12'; ?>">
              <?php if ($kitItems === null): ?>
                <div class="zen-alert zen-alert--warning">
                  <p>Sorry, we couldn't load the essential kit items at this time. Please try refreshing the page.</p>
                  <!-- <div class="zen-alert__debug">
                    <p><strong>JSON Error:</strong> <?php echo $jsonErrorMsg; ?> (Code: <?php echo $jsonError; ?>)</p>
                    <p><strong>Raw Content:</strong> <?php echo htmlspecialchars($content); ?></p>
                    <p><strong>Content Length:</strong> <?php echo strlen($content); ?> characters</p>
                    <p><strong>First 100 characters:</strong> <?php echo htmlspecialchars(substr($content, 0, 100)); ?></p>
                  </div> -->
                </div>
              <?php elseif (empty($kitItems)): ?>
                <div class="zen-alert zen-alert--info">
                  <p>No essential kit items have been added for this trip yet.</p>
                  <?php if (defined('JDEBUG') && JDEBUG): ?>
                    <div class="zen-alert__debug">
                      <p><strong>Debug Info:</strong></p>
                      <ul>
                        <li>JSON decoded successfully but array is empty</li>
                        <li>Raw Content Length: <?php echo strlen($item->content); ?> characters</li>
                        <li>Content Preview: <?php echo htmlspecialchars(substr($item->content, 0, 100)) . (strlen($item->content) > 100 ? '...' : ''); ?></li>
                      </ul>
                    </div>
                  <?php endif; ?>
                </div>
              <?php elseif (is_array($kitItems)): ?>
                <div class="essential-kit-carousel">
                  <div class="essential-kit-slider">
                    <?php
                    foreach ($kitItems as $kit):
                    ?>
                      <div class="essential-kit-slide">
                        <div class="essential-kit-item">
                          <?php if (!empty($kit['image'])): ?>
                            <div class="essential-kit-image-container">
                              <img src="<?php echo htmlspecialchars($kit['image']); ?>" alt="<?php echo htmlspecialchars($kit['item_name'] ?? ''); ?>" class="essential-kit-image">
                            </div>
                          <?php endif; ?>
                          <?php if (!empty($kit['item_name'])): ?>
                            <p class="essential-kit-title"><strong><?php echo htmlspecialchars($kit['item_name']); ?></strong></p>
                          <?php endif; ?>
                          <?php if (!empty($kit['item_description'])): ?>
                            <p class="essential-kit-description"><?php echo htmlspecialchars($kit['item_description']); ?></p>
                          <?php endif; ?>
                          <?php if (!empty($kit['product_name'])): ?>
                            <p class="essential-kit-product"><strong><?php echo htmlspecialchars($kit['product_name']); ?></strong></p>
                          <?php endif; ?>
                        </div>
                      </div>
                    <?php
                    endforeach;
                    ?>
                    <div class="slide-spacer"></div>
                  </div>
                </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        <?php endif; ?>
        <?php if ($freeEquipmentHire): ?>
        <div class="col-12 col-md-12 lg-4 col-split-30">
          <div class="equipment-hire-panel">
            <h4>FREE equipment hire!</h4>
            <p class="strong">Already booked on this trip? Or looking to book on one of our trips?</p>
            <p>ALL EverTrekkers have free hire of our winter sleeping bags and a insulated down jacket worth £110 – Just let us know when you book and we can get these reserved for you.</p>
            <p>You also get a FREE 80 Ltr EverTrek duffel bag, t-shirt, cap, and map to keep when you arrive in Kathmandu!</p>
          </div>
        </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
  <?php endif; ?>

  <style>
    .essential-kit-carousel {
      position: relative;
    }
    .essential-kit-item {
      text-align: left;
      width: min-content;
    }
    .essential-kit-item a {
      text-decoration: none;
    }
    .essential-kit-image-container {
      width: 207px;
      height: 207px;
      background-color: #efefef;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      overflow: hidden;
    }
    .essential-kit-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border: none !important;
    }
    .essential-kit-title {
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
    }
    .essential-kit-description {
      font-size: 0.9rem;
      color: black;
      margin-bottom: 5px;
    }
    .essential-kit-product, .essential-kit-title {
      font-size: 0.9rem;
      color: black;
      margin-bottom: 5px;
    }

    .equipment-hire-panel {
      background-color: white;
      padding: 20px;
      padding-bottom: 20px;
      border-radius: 10px;
      /* height: 100%; */
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.15);
    }
    .equipment-hire-panel p {
      margin-bottom: 1rem;
    }
    .equipment-hire-panel p:last-child {
      margin-bottom: 0;
    }
    .equipment-hire-panel .strong {
      font-weight: 600;
    }
    .essential-kit-slider {
      position: static;
      margin: 0 -10px;
      width: calc(100% + 20px);
    }
    .essential-kit-slider .slick-list {
      margin: 0;
    }
    .essential-kit-slider .slick-prev,
    .essential-kit-slider .slick-next {
      z-index: 1;
      width: 40px;
      height: 40px;
      top: calc(50% - 140px);
      transform: translateY(-50%);
    }
    .essential-kit-slider .slick-prev {
      left: -50px;
    }
    .essential-kit-slider .slick-next {
      right: -50px;
    }
    .essential-kit-slide {
      padding: 0 10px;
    }
    .slide-spacer {
      width: 1px;
      flex-shrink: 0; /* Prevent it from shrinking */
      height: 1px; /* Or any minimal height.  Doesn't matter */
    }
    .col-split-65 {
      width: 100%;
    }
    .col-split-30 {
      width: 100%;
    }
    .btn-outline-kit {
      display: inline-flex;
      align-items: center;
      background: transparent;
      border: 1px solid #000;
      color: #000;
      padding: 12px 24px;
      border-radius: 100px;
      font-size: 16px;
      line-height: 1.5;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .btn-outline-kit:hover {
      background: rgba(0,0,0,0.1);
    }
    .btn-outline-kit img {
      width: 20px;
      height: 20px;
    }
    @media (min-width: 992px) {
      .row.gx-0 {
        gap: 30px;
        display: flex;
        flex-wrap: nowrap;
      }
      .slide-spacer {
        display: none!important;
    }
    .essential-kit-slide {
      min-width: 254px;
    }
      .col-split-65 {
        flex: 0 0 calc((100% - 30px) * 0.7);
        max-width: calc((100% - 30px) * 0.7);
      }
      .col-split-30 {
        flex: 0 0 calc((100% - 30px) * 0.3);
        max-width: calc((100% - 30px) * 0.3);
      }
    }
    @media (max-width: 991px) {
      .essential-kit-carousel {
        margin-left: 5px;
        margin-right: -15px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: x mandatory;
        padding-bottom: 20px;
      }
      .essential-kit-slider {
        margin: 0;
        width: 100%;
      }
      .essential-kit-slider:not(.slick-initialized) {
        display: flex;
        gap: 20px;
        padding: 0 15px;
      }
      .essential-kit-slide {
        flex: 0 0 207px;
        scroll-snap-align: start;
        padding: 0;
      }

      .essential-kit-slide:last-child {
        padding-right: 15px;
      }

      .essential-kit-slider .slick-prev,
      .essential-kit-slider .slick-next {
        display: none !important;
      }
    }
  </style>
  <script>
    (function() {
      // Debug flag - set to false to mute general logging
      var DEBUG = false;

      // Constants
      var DESKTOP_BREAKPOINT = 992;
      var SLIDER_SELECTOR = '.essential-kit-slider';
      var TAB_CONTENT_SELECTOR = '#trip-overview-tab-content';

      // State tracking
      var sliderState = {
        initialized: false,
        mode: null, // 'desktop' or 'mobile'
        tabActive: false
      };

      // Helper function for consistent logging
      function log(message, data) {
        if (!DEBUG) return;

        var prefix = '[KITSLIDER] ';
        if (data !== undefined) {
          console.log(prefix + message, data);
        } else {
          console.log(prefix + message);
        }
      }

      // Special logger for key slider lifecycle events
      function logLifecycle(action, details) {
        // Always log lifecycle events regardless of DEBUG setting
        var prefix = '[KITSLIDER-LIFECYCLE] ';
        var message = action;

        if (details !== undefined) {
          console.log(prefix + message, details);
        } else {
          console.log(prefix + message);
        }
      }

      // Wait for jQuery and slick to be available
      function waitForDependencies(callback) {
        if (typeof jQuery === 'undefined') {
          setTimeout(function() { waitForDependencies(callback); }, 100);
          return;
        }

        if (typeof jQuery.fn.slick === 'undefined') {
          setTimeout(function() { waitForDependencies(callback); }, 100);
          return;
        }

        log('Dependencies loaded');
        callback(jQuery);
      }

      // Check if we're in desktop mode
      function isDesktop() {
        return jQuery(window).width() > DESKTOP_BREAKPOINT;
      }

      // Check if jQuery and slick are available
      function areScriptsAvailable() {
        if (typeof jQuery === 'undefined') {
          log('jQuery is not available');
          return false;
        }

        if (typeof jQuery.fn.slick === 'undefined') {
          log('Slick plugin is not available');
          return false;
        }

        return true;
      }

      // Check if slider has a valid Slick instance
      function hasValidSlickInstance($slider) {
        if (!areScriptsAvailable()) {
          log('Scripts not available when checking for valid instance');
          return false;
        }

        if (!$slider || !$slider.length) {
          log('Slider element not found when checking for valid instance');
          return false;
        }

        // First check for the initialized class
        if (!$slider.hasClass('slick-initialized')) {
          log('Slider does not have slick-initialized class');
          return false;
        }

        // Try to access the Slick object
        try {
          // First check if the slick method exists on this jQuery object
          if (typeof $slider.slick !== 'function') {
            log('Slick method not available on jQuery object');
            return false;
          }

          // Try to get the current slide - this is a simpler check than getSlick
          var currentSlide = $slider.slick('slickCurrentSlide');
          log('Current slide index: ' + currentSlide);

          log('Slider has valid Slick instance');
          return true;
        } catch (error) {
          log('Error accessing Slick methods, instance is invalid', error);
          return false;
        }
      }

      // Diagnose slider state
      function diagnoseSlider($slider) {
        if (!$slider || !$slider.length) {
          log('Slider element not found during diagnosis');
          return;
        }

        log('Diagnosing slider state:');
        log('- Has slick-initialized class: ' + $slider.hasClass('slick-initialized'));
        log('- Number of slides: ' + $slider.find('.essential-kit-slide').length);
        log('- Slider width: ' + $slider.width() + 'px');
        log('- Slider is visible: ' + $slider.is(':visible'));

        // Check DOM structure
        log('- Has slick-list: ' + ($slider.find('.slick-list').length > 0));
        log('- Has slick-track: ' + ($slider.find('.slick-track').length > 0));
        log('- Number of slick-slides: ' + $slider.find('.slick-slide').length);

        // Check buttons
        var $prevBtn = $slider.find('.slick-prev');
        var $nextBtn = $slider.find('.slick-next');

        log('- Prev button exists: ' + ($prevBtn.length > 0));
        log('- Next button exists: ' + ($nextBtn.length > 0));

        if ($prevBtn.length) {
          log('- Prev button visible: ' + $prevBtn.is(':visible'));
          log('- Prev button dimensions: ' + $prevBtn.width() + 'x' + $prevBtn.height());
          log('- Prev button CSS:', {
            display: $prevBtn.css('display'),
            visibility: $prevBtn.css('visibility'),
            opacity: $prevBtn.css('opacity'),
            position: $prevBtn.css('position'),
            zIndex: $prevBtn.css('z-index'),
            pointerEvents: $prevBtn.css('pointer-events')
          });
        }

        if ($nextBtn.length) {
          log('- Next button visible: ' + $nextBtn.is(':visible'));
          log('- Next button dimensions: ' + $nextBtn.width() + 'x' + $nextBtn.height());
          log('- Next button CSS:', {
            display: $nextBtn.css('display'),
            visibility: $nextBtn.css('visibility'),
            opacity: $nextBtn.css('opacity'),
            position: $nextBtn.css('position'),
            zIndex: $nextBtn.css('z-index'),
            pointerEvents: $nextBtn.css('pointer-events')
          });
        }

        // Check if Slick instance is valid
        if (hasValidSlickInstance($slider)) {
          try {
            var slickObj = $slider.slick('getSlick');
            log('- Slick object properties:', {
              slideCount: slickObj.slideCount,
              currentSlide: slickObj.currentSlide,
              options: {
                slidesToShow: slickObj.options.slidesToShow,
                infinite: slickObj.options.infinite,
                arrows: slickObj.options.arrows
              }
            });
          } catch (error) {
            log('- Error accessing Slick object properties', error);
          }
        }
      }

      // Completely reinitialize the slider element
      function forceReinitializeSlider($slider) {
        if (!areScriptsAvailable()) {
          logLifecycle("REINITIALIZATION ABORTED - Scripts not available");
          return false;
        }

        if (!$slider || !$slider.length) {
          logLifecycle("REINITIALIZATION ABORTED - Slider element not found");
          return false;
        }

        try {
          logLifecycle("REINITIALIZATION STARTED");

          // Get the original HTML content
          var originalHtml = $slider.html();

          // Check if we have slides
          var slideCount = $slider.find('.essential-kit-slide').length;
          log('Number of slides before reinitialization: ' + slideCount);

          if (slideCount === 0) {
            logLifecycle("REINITIALIZATION ABORTED - No slides found");
            return false;
          }

          // Remove all slick-related classes and elements
          if ($slider.hasClass('slick-initialized')) {
            try {
              logLifecycle("UNSLICK ATTEMPT - Before reinitialization");
              $slider.slick('unslick');
              logLifecycle("UNSLICK SUCCESS");
            } catch (e) {
              logLifecycle("UNSLICK FAILED - Will recreate element", e);

              // If unslick fails, recreate the element
              var $parent = $slider.parent();
              var sliderClass = $slider.attr('class');
              var sliderId = $slider.attr('id');

              logLifecycle("ELEMENT REMOVAL - Removing slider element");
              // Remove the slider
              $slider.remove();

              logLifecycle("ELEMENT CREATION - Creating new slider element");
              // Create a new slider element
              var $newSlider = jQuery('<div>');
              if (sliderClass) $newSlider.attr('class', sliderClass);
              if (sliderId) $newSlider.attr('id', sliderId);

              // Add the original content
              $newSlider.html(originalHtml);

              // Add it back to the parent
              $parent.append($newSlider);

              // Update the slider reference
              $slider = $newSlider;
              logLifecycle("ELEMENT RECREATION COMPLETE");
            }
          }

          logLifecycle("INITIALIZATION STARTED - With fresh settings");

          // Check if free equipment hire is enabled
          var $kitCarousel = $slider.closest('.essential-kit-carousel');
          var $parentCol = $kitCarousel.closest('.col-12');
          var hasFreeEquipmentHire = $parentCol.hasClass('col-md-8') || $parentCol.hasClass('col-split-65');

          // Set slidesToShow based on free equipment hire status
          var slidesToShow = hasFreeEquipmentHire ? 3 : 4;
          log('Free equipment hire: ' + hasFreeEquipmentHire + ', slidesToShow: ' + slidesToShow);

          // Initialize with desktop settings
          $slider.slick({
            slidesToShow: slidesToShow,
            slidesToScroll: 1,
            dots: false,
            arrows: true,
            infinite: true,
            prevArrow: '<button type="button" class="slick-prev"><img src="/templates/zenbase/icons/trips/arrow_back.svg" alt="Previous" /></button>',
            nextArrow: '<button type="button" class="slick-next"><img src="/templates/zenbase/icons/trips/arrow_forward.svg" alt="Next" /></button>',
            responsive: [
              {
                breakpoint: 1300,
                settings: {
                  slidesToShow: slidesToShow,
                  slidesToScroll: 1
                }
              },
              {
                breakpoint: 1200,
                settings: {
                  slidesToShow: 2,
                  slidesToScroll: 1
                }
              },
              {
                breakpoint: DESKTOP_BREAKPOINT,
                settings: 'unslick'
              }
            ]
          });

          // Add event listeners to monitor slider events
          $slider.on('beforeChange', function(event, slick, currentSlide, nextSlide) {
            log('Slider beforeChange event - current: ' + currentSlide + ', next: ' + nextSlide);
          });

          $slider.on('afterChange', function(event, slick, currentSlide) {
            log('Slider afterChange event - current: ' + currentSlide);
          });

          // Add event listeners to monitor button clicks
          var $prevBtn = $slider.find('.slick-prev');
          var $nextBtn = $slider.find('.slick-next');

          $prevBtn.on('click', function(e) {
            log('Prev button clicked');
          });

          $nextBtn.on('click', function(e) {
            log('Next button clicked');
          });

          logLifecycle("REINITIALIZATION COMPLETE");
          return true;
        } catch (error) {
          logLifecycle("REINITIALIZATION FAILED", error);
          return false;
        }
      }

      // Initialize the slider for desktop
      function initializeSlider($slider) {
        if (!areScriptsAvailable()) {
          logLifecycle("INITIALIZATION ABORTED - Scripts not available");
          return false;
        }

        if (!$slider || !$slider.length) {
          logLifecycle("INITIALIZATION ABORTED - Slider element not found");
          return false;
        }

        try {
          logLifecycle("INITIALIZATION STARTED");

          // Check if we have slides
          var slideCount = $slider.find('.essential-kit-slide').length;

          if (slideCount === 0) {
            logLifecycle("INITIALIZATION ABORTED - No slides found");
            return false;
          }

          // If already initialized, use force reinitialization
          if ($slider.hasClass('slick-initialized')) {
            logLifecycle("INITIALIZATION REDIRECTED - Slider already initialized, using force reinitialization");
            return forceReinitializeSlider($slider);
          }

          // Check if free equipment hire is enabled
          var $kitCarousel = $slider.closest('.essential-kit-carousel');
          var $parentCol = $kitCarousel.closest('.col-12');
          var hasFreeEquipmentHire = $parentCol.hasClass('col-md-8') || $parentCol.hasClass('col-split-65');

          // Set slidesToShow based on free equipment hire status
          var slidesToShow = hasFreeEquipmentHire ? 3 : 4;
          log('Free equipment hire: ' + hasFreeEquipmentHire + ', slidesToShow: ' + slidesToShow);

          // Initialize with desktop settings
          $slider.slick({
            slidesToShow: slidesToShow,
            slidesToScroll: 1,
            dots: false,
            arrows: true,
            infinite: true,
            prevArrow: '<button type="button" class="slick-prev"><img src="/templates/zenbase/icons/trips/arrow_back.svg" alt="Previous" /></button>',
            nextArrow: '<button type="button" class="slick-next"><img src="/templates/zenbase/icons/trips/arrow_forward.svg" alt="Next" /></button>',
            responsive: [
              {
                breakpoint: 1300,
                settings: {
                  slidesToShow: slidesToShow,
                  slidesToScroll: 1
                }
              },
              {
                breakpoint: 1200,
                settings: {
                  slidesToShow: 2,
                  slidesToScroll: 1
                }
              },
              {
                breakpoint: DESKTOP_BREAKPOINT,
                settings: 'unslick'
              }
            ]
          });

          // Add event listeners to monitor slider events
          $slider.on('beforeChange', function(event, slick, currentSlide, nextSlide) {
            log('Slider beforeChange event - current: ' + currentSlide + ', next: ' + nextSlide);
          });

          $slider.on('afterChange', function(event, slick, currentSlide) {
            log('Slider afterChange event - current: ' + currentSlide);
          });

          // Add event listeners to monitor button clicks
          var $prevBtn = $slider.find('.slick-prev');
          var $nextBtn = $slider.find('.slick-next');

          $prevBtn.on('click', function(e) {
            log('Prev button clicked');
          });

          $nextBtn.on('click', function(e) {
            log('Next button clicked');
          });

          logLifecycle("INITIALIZATION COMPLETE");
          return true;
        } catch (error) {
          logLifecycle("INITIALIZATION FAILED", error);
          return false;
        }
      }

      // Uninitialize the slider for mobile
      function uninitializeSlider($slider) {
        if (!areScriptsAvailable()) {
          logLifecycle("UNINITIALIZATION ABORTED - Scripts not available");
          return false;
        }

        if (!$slider || !$slider.length) {
          logLifecycle("UNINITIALIZATION ABORTED - Slider element not found");
          return false;
        }

        try {
          if ($slider.hasClass('slick-initialized')) {
            logLifecycle("UNINITIALIZATION STARTED - For mobile view");
            $slider.slick('unslick');
            logLifecycle("UNINITIALIZATION COMPLETE");
          } else {
            log('Slider already uninitialized');
          }
          return true;
        } catch (error) {
          logLifecycle("UNINITIALIZATION FAILED", error);
          return false;
        }
      }

      // Handle slider mode based on screen size
      function handleSliderMode() {
        var $slider = jQuery(SLIDER_SELECTOR);
        var desktop = isDesktop();
        var currentlyInitialized = $slider.hasClass('slick-initialized');

        log('Handling slider mode - desktop: ' + desktop + ', initialized: ' + currentlyInitialized + ', previous mode: ' + sliderState.mode);

        // Check if tab is visible
        var $tabContent = jQuery(TAB_CONTENT_SELECTOR);
        var tabVisible = $tabContent.is(':visible') || $tabContent.hasClass('show') || $tabContent.hasClass('active');

        log('Tab visibility - visible: ' + tabVisible);

        // Update state
        sliderState.tabActive = tabVisible;

        // Handle desktop mode
        if (desktop) {
          if (sliderState.mode !== 'desktop') {
            log('Switching to desktop mode');

            // Only initialize if tab is visible
            if (tabVisible) {
              if (initializeSlider($slider)) {
                sliderState.initialized = true;
                sliderState.mode = 'desktop';
              }
            } else {
              log('Tab not visible, deferring initialization');
              sliderState.mode = 'desktop';
              sliderState.initialized = false;
            }
          } else if (tabVisible && !currentlyInitialized) {
            // Tab is now visible but slider not initialized
            log('Tab now visible in desktop mode, initializing slider');
            if (initializeSlider($slider)) {
              sliderState.initialized = true;
            }
          } else if (currentlyInitialized) {
            // Already in desktop mode and initialized, just refresh
            log('Already in desktop mode, refreshing slider');
            try {
              // Check if the Slick instance is still valid before calling setPosition
              if (hasValidSlickInstance($slider)) {
                $slider.slick('setPosition');
                log('Slider position refreshed successfully');
              } else {
                log('Slick instance is no longer valid, reinitializing');
                initializeSlider($slider);
              }
            } catch (error) {
              log('Error refreshing slider position', error);
              // Try to reinitialize if refresh fails
              log('Attempting to reinitialize slider after refresh error');
              initializeSlider($slider);
            }
          }
        }
        // Handle mobile mode
        else {
          if (sliderState.mode !== 'mobile') {
            log('Switching to mobile mode');

            if (uninitializeSlider($slider)) {
              sliderState.initialized = false;
              sliderState.mode = 'mobile';
            }
          }
        }
      }

      // Handle tab activation
      function handleTabActivation() {
        log('Tab activated');

        // Short delay to let the DOM update
        setTimeout(function() {
          var $slider = jQuery(SLIDER_SELECTOR);
          var desktop = isDesktop();
          var currentlyInitialized = $slider.hasClass('slick-initialized');

          log('Tab activation - desktop: ' + desktop + ', initialized: ' + currentlyInitialized);

          // Update state
          sliderState.tabActive = true;

          if (desktop) {
            if (!currentlyInitialized) {
              log('Desktop mode but not initialized after tab activation, initializing');
              if (initializeSlider($slider)) {
                sliderState.initialized = true;
                sliderState.mode = 'desktop';
              }
            } else {
              log('Desktop mode and already initialized, checking instance');
              try {
                // Check if the Slick instance is still valid
                if (hasValidSlickInstance($slider)) {
                  log('Refreshing slider after tab activation');
                  $slider.slick('setPosition');
                  log('Slider refreshed successfully after tab activation');
                } else {
                  log('Slick instance is invalid after tab activation, reinitializing');
                  if (initializeSlider($slider)) {
                    sliderState.initialized = true;
                    sliderState.mode = 'desktop';
                  }
                }
              } catch (error) {
                log('Error refreshing slider after tab activation', error);
                log('Attempting to reinitialize slider after tab activation error');
                if (initializeSlider($slider)) {
                  sliderState.initialized = true;
                  sliderState.mode = 'desktop';
                }
              }
            }
          }
        }, 100);
      }

      // Main initialization function
      function initialize() {
        log('Starting initialization');

        waitForDependencies(function($) {
          log('Setting up essential kit slider');

          // Initial state setup
          var desktop = isDesktop();
          var $tabContent = $(TAB_CONTENT_SELECTOR);
          var tabVisible = $tabContent.is(':visible') || $tabContent.hasClass('show') || $tabContent.hasClass('active');

          log('Initial state - desktop: ' + desktop + ', tab visible: ' + tabVisible);

          // Update state
          sliderState.mode = desktop ? 'desktop' : 'mobile';
          sliderState.tabActive = tabVisible;

          // Initial setup
          handleSliderMode();

          // Set up event listeners

          // Debounced resize handler
          var resizeTimer;
          $(window).on('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
              log('Window resized');
              handleSliderMode();
            }, 250);
          });

          // Tab activation handler
          $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
            if (e.target.getAttribute('href') === TAB_CONTENT_SELECTOR) {
              log('Overview tab shown');
              handleTabActivation();
            }
          });

          // Mobile accordion handler
          $('.js-mobile-tabs').on('shown.bs.collapse', function() {
            if ($(this).attr('data-bs-target') === TAB_CONTENT_SELECTOR) {
              log('Overview accordion shown');
              handleTabActivation();
            }
          });

          // Deep linking integration
          if (window.navigateToSection) {
            log('Deep linking detected, adding integration');

            // Store the original navigateToSection function
            var originalNavigateToSection = window.navigateToSection;

            // Override with our version that handles the slider
            window.navigateToSection = function(hash) {
              log('Deep linking navigation to: ' + hash);

              // Call the original function
              originalNavigateToSection(hash);

              // If navigating to overview tab, ensure slider is initialized
              if (hash === 'trip-overview') {
                log('Deep linking to overview tab, checking slider');
                setTimeout(handleTabActivation, 500);
              }
            };
          }

          // Window load event for final check
          $(window).on('load', function() {
            logLifecycle("WINDOW LOAD EVENT - Final check");

            // Longer delay to ensure everything is ready
            setTimeout(function() {
              // Check if scripts are available
              if (!areScriptsAvailable()) {
                logLifecycle("WINDOW LOAD CHECK DELAYED - Scripts not available, will retry");

                // Try again after a longer delay
                setTimeout(function() {
                  if (!areScriptsAvailable()) {
                    logLifecycle("WINDOW LOAD CHECK ABORTED - Scripts still not available after retry");
                    return;
                  }

                  performWindowLoadCheck();
                }, 1000);

                return;
              }

              performWindowLoadCheck();
            }, 1000);

            // Function to perform the window load check
            function performWindowLoadCheck() {
              var $slider = $(SLIDER_SELECTOR);
              var desktop = isDesktop();
              var $tabContent = $(TAB_CONTENT_SELECTOR);
              var tabVisible = $tabContent.is(':visible') || $tabContent.hasClass('show') || $tabContent.hasClass('active');

              if (desktop && tabVisible) {
                logLifecycle("WINDOW LOAD REINITIALIZATION - Desktop mode and tab visible");

                // Always use force reinitialization on window load to ensure a clean state
                forceReinitializeSlider($slider);
              } else {
                if (!desktop) {
                  logLifecycle("WINDOW LOAD CHECK SKIPPED - Mobile mode detected");
                } else if (!tabVisible) {
                  logLifecycle("WINDOW LOAD CHECK SKIPPED - Tab not visible");
                }
              }
            }
          });

          // Add global test function that can be called from the console
          window.testEssentialKitSlider = function() {
            log('Manual slider test initiated');

            var $slider = $(SLIDER_SELECTOR);
            log('Slider found: ' + ($slider.length > 0));

            if (!$slider.length) {
              log('Slider element not found');
              return;
            }

            // Run diagnostics
            diagnoseSlider($slider);

            // Check if slider has a valid instance
            var hasValidInstance = hasValidSlickInstance($slider);
            log('Has valid Slick instance: ' + hasValidInstance);

            if (!hasValidInstance) {
              log('Attempting to reinitialize slider');
              if (initializeSlider($slider)) {
                log('Reinitialization successful');
                diagnoseSlider($slider);
              } else {
                log('Reinitialization failed');
              }
              return;
            }

            // Test navigation
            log('Testing programmatic navigation');

            try {
              // Get current slide
              var currentSlide = $slider.slick('slickCurrentSlide');
              log('Current slide before navigation: ' + currentSlide);

              // Go to next slide
              log('Calling slickNext()');
              $slider.slick('slickNext');

              setTimeout(function() {
                try {
                  var newSlide = $slider.slick('slickCurrentSlide');
                  log('Current slide after slickNext(): ' + newSlide);

                  // Go to previous slide
                  log('Calling slickPrev()');
                  $slider.slick('slickPrev');

                  setTimeout(function() {
                    try {
                      var finalSlide = $slider.slick('slickCurrentSlide');
                      log('Current slide after slickPrev(): ' + finalSlide);

                      // Check if navigation worked
                      if (finalSlide === currentSlide) {
                        log('Navigation test successful - returned to original slide');
                      } else {
                        log('Navigation test anomaly - did not return to original slide');
                      }

                      // Final diagnostics
                      diagnoseSlider($slider);
                    } catch (error) {
                      log('Error during slickPrev test', error);
                    }
                  }, 500);
                } catch (error) {
                  log('Error during slickNext test', error);
                }
              }, 500);
            } catch (error) {
              log('Error during navigation test', error);
            }
          };

          log('Manual test function added. Call window.testEssentialKitSlider() to test.');
        });
      }

      // Start initialization
      initialize();
    })();
  </script>

  <!-- Section 8: Sustainable Tourism -->
  <div class="overview-section" id="overview-sustainable-tourism">
    <div class="container">
      <div class="sustainable-tourism-panel">
        <div class="d-flex align-items-center flex-column flex-md-row">
          <img src="/templates/zenbase/icons/trips/plant.svg" alt="Sustainable Tourism" width="92" height="98" class="sustainable-tourism-icon">
          <div>
            <h3 class="mb-2">Sustainable Tourism</h3>
            <p class="intro mb-0">Because we only use local guiding teams it ensures your trip directly benefits the local mountain communities.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Section 9: What customers say about us (only when trip extensions are available) -->
  <?php if ((isset($ugcReviews) && !empty($ugcReviews)) && (isset($this->item->activities) && !empty($this->item->activities))): ?>
  <div class="overview-section" id="overview-customer-reviews">
    <div class="container">
      <h3 class="mb-4">What customers say about us</h3>
      <!-- Section 9 content -->
       <div>
<!-- <div class="embedsocial-hashtag" data-ref="816b4a7734f4188b5848cf0b506066f9b374b3ed"></div> <script> (function(d, s, id) { var js; if (d.getElementById(id)) {return;} js = d.createElement(s); js.id = id; js.src = "https://embedsocial.com/cdn/ht.js"; d.getElementsByTagName("head")[0].appendChild(js); }(document, "script", "EmbedSocialHashtagScript")); </script> -->

  <div class="embedsocial-widget" data-ref="fc2d1a20d46e3a578af0152614a2d130" style=""></div> <script> (function(d, s, id) { var js; if (d.getElementById(id)) {return;} js = d.createElement(s); js.id = id; js.src = "https://embedsocial.com/cdn/aht.js"; d.getElementsByTagName("head")[0].appendChild(js); }(document, "script", "EmbedSocialWidgetScript")); </script>

<style>
          .embedsocial-widget {
          width: 100%;
          max-width: 100%;
          }
          @media screen and (min-width: 767px) {
          .embedsocial-widget {
            max-width: 1440px;
          }
          .embedsocial-widget iframe {
            margin: -2.5rem -1.25rem;
            width: 103%!important;
          }

</style>

          <style>
          .embedsocial-widget {
          width: 100%;
          max-width: 100%;
          }
          @media screen and (min-width: 767px) {
          .embedsocial-widget {
            max-width: 1440px;
          }
          .embedsocial-widget iframe {
            margin: -2.5rem -1.25rem;
            width: 103%!important;
          }
          }

          </style>
       </div>
    </div>
	</div>
  <?php endif; ?>

</div>

<style>
.overview-section {
  width: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 4rem 0;
}

.overview-section > .container {
  position: relative;
}

.trip-map {
  position: relative;
  margin-top: 2rem;
}

.trip-map img {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 2.4%;
}

.overview-section--dark p {
  margin-bottom: 2px;
}

.trip-guide-panel {
  background-color: white;
  padding: 10px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.trip-guide-panel .zen-media {
  background-color: transparent;
  padding: 0;
  border: none;
  margin-bottom: 0;
}

.trip-guide-panel .zen-media__title {
  margin-bottom: 0;
}

.trip-guide-panel .btn {
  font-size: 12px;
  line-height: 150%;
  font-weight: 500;
  margin: 5px !important;
}

.trip-guide-panel .btn img {
  width: 18px;
  height: 18px;
}

#overview-trip-highlights, #overview-trip-extensions {
  background-color: #f2f2f2;
  background-image: url('/templates/zenbase/images/backgrounds/forest_stack.jpg');
  background-attachment: fixed;
}

#overview-the-evertrek-difference { background-color: #e3e8ee;}

#overview-whats-included, #overview-trek-challenge  {
  background-color: #247ba0;
  background-image: url('/templates/zenbase/images/backgrounds/contours_stack.jpg');
  background-size: 100% 100%;
}

#overview-whats-included h3,
#overview-whats-included p,
#overview-whats-included li,
#overview-whats-included a {
  color: white;
}
#overview-trek-challenge h3,
#overview-trek-challenge p,
#overview-trek-challenge li,
#overview-trek-challenge a {
  color: white;
}

#overview-essential-kit {
  background-image: url('/templates/zenbase/images/backgrounds/peaks_stack.jpg');
}

/* TEMP */
#overview-essential-kit {
  /* display:none; */
}
#overview-customer-reviews {
  background-image: url('/templates/zenbase/images/backgrounds/peaks_stack.jpg');
}

/* /TEMP */

#overview-essential-kit .row:first-child [class^="col-"]:last-child a {
  margin-top: 10px;
}

#overview-sustainable-tourism {
  padding: 48px 0;
  background-color: #f2f2f2;

}

.sustainable-tourism-panel {
  border-radius: 10px;
  /* height: 194px; */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 48px 40px 48px;
  color: white;
  position: relative;
  overflow: hidden;
}

.sustainable-tourism-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('/templates/zenbase/images/backgrounds/mountains.jpg') 50% 50% no-repeat;
  background-size: cover; /* 110%; */
  filter: grayscale(100%);
  z-index: 0;
}

.sustainable-tourism-panel .d-flex {
  position: relative;
  z-index: 1;
}

.sustainable-tourism-panel h3,
.sustainable-tourism-panel p {
  color: white;
}


#overview-sustainable-tourism img {
  filter: invert(54%) sepia(85%) saturate(2693%) hue-rotate(346deg) brightness(101%) contrast(97%);
}

#overview-customer-reviews { background-color: #f2f2f2;}

/* Customer reviews alternative section (when no trip extensions) */
#overview-customer-reviews-alt {
  background-color: #f2f2f2;
  background-image: url('/templates/zenbase/images/backgrounds/forest_stack.jpg');
  background-attachment: fixed;
}

.zen-holiday__content {
  padding: 0 !important;
}
.zen-holiday__tabs {
  margin: 0 !important;
}

.sustainable-tourism-icon {
  margin-bottom: 25px;
}

@media (min-width: 768px) {
  .sustainable-tourism-icon {
    margin-bottom: 0;
    margin-right: 60px;
  }
  .training-plan-icon {
    height: 93px;
  }
}

.zen-rte--default-list-ticks ul,
.zen-rte--default-list-crosses ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.zen-rte--default-list-ticks li,
.zen-rte--default-list-crosses li {
  position: relative;
  padding-left: 32px;
  margin-bottom: 8px;
}

.zen-rte--default-list-ticks li::before,
.zen-rte--default-list-crosses li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 20px;
  height: 20px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.zen-rte--default-list-ticks li::before {
  background-image: url('/templates/zenbase/icons/trips/check_circle.svg');
}

.zen-rte--default-list-crosses li::before {
  background-image: url('/templates/zenbase/icons/trips/cancel.svg');
}

#overview-whats-included .zen-rte--default-list-ticks ul li::before,
#overview-whats-included .zen-rte--default-list-ticks li::before,
#overview-whats-included .zen-rte--default-list-crosses ul li::before,
#overview-whats-included .zen-rte--default-list-crosses li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

#overview-whats-included .zen-rte--default-list-ticks ul li,
#overview-whats-included .zen-rte--default-list-ticks li,
#overview-whats-included .zen-rte--default-list-crosses ul li,
#overview-whats-included .zen-rte--default-list-crosses li {
  position: relative;
  padding-left: 29px;
  margin-bottom: 8px;
}

#overview-whats-included .zen-rte--default-list-ticks ul li::before,
#overview-whats-included .zen-rte--default-list-ticks li::before {
  background-image: url('/templates/zenbase/icons/trips/check_circle.svg');
}

#overview-whats-included .zen-rte--default-list-crosses ul li::before,
#overview-whats-included .zen-rte--default-list-crosses li::before {
  background-image: url('/templates/zenbase/icons/trips/cancel.svg');
}

/* Legacy support for old ID */
#whats-included .zen-rte--default-list-ticks ul li::before,
#whats-included .zen-rte--default-list-ticks li::before,
#whats-included .zen-rte--default-list-crosses ul li::before,
#whats-included .zen-rte--default-list-crosses li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

#whats-included .zen-rte--default-list-ticks ul li,
#whats-included .zen-rte--default-list-ticks li,
#whats-included .zen-rte--default-list-crosses ul li,
#whats-included .zen-rte--default-list-crosses li {
  position: relative;
  padding-left: 29px;
  margin-bottom: 8px;
}

#whats-included .zen-rte--default-list-ticks ul li::before,
#whats-included .zen-rte--default-list-ticks li::before {
  background-image: url('/templates/zenbase/icons/trips/check_circle.svg');
}

#whats-included .zen-rte--default-list-crosses ul li::before,
#whats-included .zen-rte--default-list-crosses li::before {
  background-image: url('/templates/zenbase/icons/trips/cancel.svg');
}

#hero-dates-prices-button img, #dates-prices-button img {
  position: relative;
  top: -1px;
}

@media (min-width: 992px) {
  .video-trigger {
    cursor: default;
    position: relative;
    pointer-events: none;
  }
  .essential-kit-carousel {
    margin-left: 40px;
    margin-right: 40px;
  }
}

.trip-highlights-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.trip-highlight-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}
.trip-highlight-icon {
  flex: 0 0 20px;
  width: 20px;
  height: 20px;
  margin-right: 12px;
}
.trip-highlight-icon svg {
  width: 20px;
  height: 20px;
}
.trip-highlight-text {
  flex: 1;
}
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');
    const heroButton = document.getElementById('hero-dates-prices-button');
    const overviewButton = document.getElementById('dates-prices-button');
    console.log('Hero Button:', heroButton);
    console.log('Overview Button:', overviewButton);

    function handleTabClick(e, selectors) {
      console.log('Button clicked');
      e.preventDefault();

      // Use the global navigateToSection function from deep-linking.js
      if (window.navigateToSection && typeof window.navigateToSection === 'function') {
        console.log('Using navigateToSection from deep-linking.js');

        // For desktop, navigate to the tab
        if (window.innerWidth >= 992) {
          window.navigateToSection(selectors.desktopTabId);
        }
        // For mobile, navigate to the accordion content
        else {
          window.navigateToSection(selectors.mobileContentId);
        }
      }
      // Fallback if navigateToSection is not available
      else {
        console.warn('navigateToSection not found, using fallback');

        if (window.innerWidth >= 992) { // Desktop
          // Get the desktop tabs container
          const tabsContainer = document.getElementById('desktop-tabs');
          console.log('Tabs Container:', tabsContainer);

          if (tabsContainer) {
            // Calculate the position to scroll to (accounting for any fixed headers)
            const headerOffset = 100; // Adjust this value based on your header height
            const elementPosition = tabsContainer.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
            console.log('Scroll Position:', {
              elementPosition,
              pageYOffset: window.pageYOffset,
              headerOffset,
              finalOffset: offsetPosition
            });

            // Smooth scroll to the tabs
            window.scrollTo({
              top: offsetPosition,
              behavior: 'smooth'
            });

            // Use Bootstrap's tab API to show the tab
            const tab = document.getElementById(selectors.desktopTabId);
            console.log('Tab:', tab);

            if (tab) {
              console.log('Attempting to show tab');
              try {
                const bootstrapTab = new bootstrap.Tab(tab);
                bootstrapTab.show();
                console.log('Tab shown successfully');
              } catch (error) {
                console.error('Error showing tab:', error);
              }
            } else {
              console.error('Tab not found');
            }
          } else {
            console.error('Tabs container not found');
          }
        } else { // Mobile
          // Get the mobile accordion section
          const accordionSection = document.querySelector(selectors.mobileAccordionSelector);
          const content = document.getElementById(selectors.mobileContentId);

          if (accordionSection && content) {
            // Close all accordion sections immediately
            const allAccordions = document.querySelectorAll('.collapse');
            allAccordions.forEach(accordion => {
              const collapse = new bootstrap.Collapse(accordion, {
                toggle: false
              });
              collapse.hide();
            });

            // Add event listener for when the accordion is shown
            content.addEventListener('shown.bs.collapse', function() {
              // Get the position of the accordion content
              const contentRect = content.getBoundingClientRect();
              const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
              const offsetPosition = contentRect.top + scrollTop - 100;

              // Scroll to the section
              window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
              });
            });

            // Open the accordion
            const collapse = new bootstrap.Collapse(content, {
              toggle: true
            });
          } else {
            console.error('Mobile accordion elements not found');
          }
        }
      }
    }

    // The deep-link-button-handlers.js script will handle these buttons automatically
    // since they have href="#dates-prices" and similar attributes

    // Log that we found the buttons for debugging purposes
    if (heroButton) {
      console.log('Hero Dates & Prices button found with href:', heroButton.getAttribute('href'));
    } else {
      console.error('Hero Dates & Prices button not found');
    }

    if (overviewButton) {
      console.log('Overview Dates & Prices button found with href:', overviewButton.getAttribute('href'));
    } else {
      console.error('Overview Dates & Prices button not found');
    }

    // Log that we found the extension links
    const extensionLinks = document.querySelectorAll('.extension-card__link');
    console.log('Found', extensionLinks.length, 'extension card links');
  });
</script>

<!-- Packing List Modal -->
<div class="modal fade" id="packingListModal" tabindex="-1" aria-labelledby="packingListModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      <div class="modal-body">
        <?php if (isset($formData)): ?>
        <h2 class="text-white text-center mt-2">Download Packing List</h2>
        <iframe
          src="<?php echo htmlspecialchars($formData['form_url']); ?>"
          style="width:100%;height:100%;border:none;border-radius:0px"
          id="<?php echo htmlspecialchars($formData['iframe_id']); ?>"
          data-layout="{'id':'INLINE'}"
          data-trigger-type="alwaysShow"
          data-trigger-value=""
          data-activation-type="alwaysActivated"
          data-activation-value=""
          data-deactivation-type="neverDeactivate"
          data-deactivation-value=""
          data-form-name="<?php echo htmlspecialchars($formData['form_name']); ?>"
          data-height="677"
          data-layout-iframe-id="<?php echo htmlspecialchars($formData['iframe_id']); ?>"
          data-form-id="<?php echo htmlspecialchars($formData['form_id']); ?>"
          title="<?php echo htmlspecialchars($formData['form_title']); ?>"
        >
        </iframe>
        <script src="https://link.evertrek.co.uk/js/form_embed.js"></script>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<style>
#packingListModal .modal-content {
  background-color: #247ba0;
  color: white;
  border: none;
  border-radius: 10px;
  min-height: 550px;
  overflow: hidden;
}
#packingListModal .btn-close {
  filter: none;
  opacity: 1;
  position: absolute;
  right: 1rem;
  top: 1rem;
  z-index: 1;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
}
#packingListModal .modal-body {
  padding: 2rem;
}
</style>




